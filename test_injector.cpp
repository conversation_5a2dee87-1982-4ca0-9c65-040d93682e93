#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <string>

// 获取进程ID通过进程名
DWORD GetProcessIdByName(const std::wstring& processName) {
    DWORD processId = 0;
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            if (_wcsicmp(pe32.szExeFile, processName.c_str()) == 0) {
                processId = pe32.th32ProcessID;
                break;
            }
        } while (Process32NextW(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    return processId;
}

// 列出所有运行的进程
void ListRunningProcesses() {
    std::wcout << L"\n=== 运行中的进程列表 ===" << std::endl;
    
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        std::wcout << L"无法创建进程快照" << std::endl;
        return;
    }
    
    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    if (Process32FirstW(hSnapshot, &pe32)) {
        std::wcout << L"PID\t进程名" << std::endl;
        std::wcout << L"---\t------" << std::endl;
        
        do {
            std::wcout << pe32.th32ProcessID << L"\t" << pe32.szExeFile << std::endl;
        } while (Process32NextW(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
}

// DLL注入函数
bool InjectDLL(DWORD processId, const std::wstring& dllPath) {
    // 打开目标进程
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!hProcess) {
        std::wcout << L"无法打开进程 " << processId << L", 错误代码: " << GetLastError() << std::endl;
        return false;
    }
    
    // 在目标进程中分配内存
    SIZE_T pathSize = (dllPath.length() + 1) * sizeof(wchar_t);
    LPVOID pRemotePath = VirtualAllocEx(hProcess, NULL, pathSize, MEM_COMMIT, PAGE_READWRITE);
    
    if (!pRemotePath) {
        std::wcout << L"无法在目标进程中分配内存, 错误代码: " << GetLastError() << std::endl;
        CloseHandle(hProcess);
        return false;
    }
    
    // 写入DLL路径
    if (!WriteProcessMemory(hProcess, pRemotePath, dllPath.c_str(), pathSize, NULL)) {
        std::wcout << L"无法写入DLL路径, 错误代码: " << GetLastError() << std::endl;
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        return false;
    }
    
    // 获取LoadLibraryW地址
    HMODULE hKernel32 = GetModuleHandleW(L"kernel32.dll");
    LPVOID pLoadLibraryW = GetProcAddress(hKernel32, "LoadLibraryW");
    
    if (!pLoadLibraryW) {
        std::wcout << L"无法获取LoadLibraryW地址" << std::endl;
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        return false;
    }
    
    // 创建远程线程
    HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, 
                                       (LPTHREAD_START_ROUTINE)pLoadLibraryW, 
                                       pRemotePath, 0, NULL);
    
    if (!hThread) {
        std::wcout << L"无法创建远程线程, 错误代码: " << GetLastError() << std::endl;
        VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
        CloseHandle(hProcess);
        return false;
    }
    
    // 等待线程完成
    WaitForSingleObject(hThread, INFINITE);
    
    // 清理资源
    VirtualFreeEx(hProcess, pRemotePath, 0, MEM_RELEASE);
    CloseHandle(hThread);
    CloseHandle(hProcess);
    
    std::wcout << L"DLL注入成功!" << std::endl;
    return true;
}

int main() {
    std::wcout << L"=== Shellcode加载器测试工具 ===" << std::endl;
    std::wcout << L"1. 列出运行进程" << std::endl;
    std::wcout << L"2. 注入DLL到指定PID" << std::endl;
    std::wcout << L"3. 注入DLL到指定进程名" << std::endl;
    std::wcout << L"4. 退出" << std::endl;
    
    int choice;
    while (true) {
        std::wcout << L"\n请选择操作 (1-4): ";
        std::wcin >> choice;
        
        switch (choice) {
        case 1:
            ListRunningProcesses();
            break;
            
        case 2: {
            DWORD pid;
            std::wcout << L"请输入目标进程PID: ";
            std::wcin >> pid;
            
            // 设置环境变量
            std::wstring pidStr = std::to_wstring(pid);
            SetEnvironmentVariableW(L"TARGET_PID", pidStr.c_str());
            
            // 获取当前DLL路径
            wchar_t dllPath[MAX_PATH];
            GetCurrentDirectoryW(MAX_PATH, dllPath);
            wcscat_s(dllPath, L"\\hijack_tester.dll");
            
            std::wcout << L"DLL路径: " << dllPath << std::endl;
            std::wcout << L"目标PID: " << pid << std::endl;
            
            if (InjectDLL(pid, dllPath)) {
                std::wcout << L"注入完成! 检查目标进程是否执行了shellcode." << std::endl;
            }
            break;
        }
        
        case 3: {
            std::wstring processName;
            std::wcout << L"请输入目标进程名 (例如: notepad.exe): ";
            std::wcin >> processName;
            
            DWORD pid = GetProcessIdByName(processName);
            if (pid == 0) {
                std::wcout << L"未找到进程: " << processName << std::endl;
                break;
            }
            
            std::wcout << L"找到进程 " << processName << L", PID: " << pid << std::endl;
            
            // 设置环境变量
            std::wstring pidStr = std::to_wstring(pid);
            SetEnvironmentVariableW(L"TARGET_PID", pidStr.c_str());
            
            // 获取当前DLL路径
            wchar_t dllPath[MAX_PATH];
            GetCurrentDirectoryW(MAX_PATH, dllPath);
            wcscat_s(dllPath, L"\\hijack_tester.dll");
            
            if (InjectDLL(pid, dllPath)) {
                std::wcout << L"注入完成! 检查目标进程是否执行了shellcode." << std::endl;
            }
            break;
        }
        
        case 4:
            std::wcout << L"退出程序." << std::endl;
            return 0;
            
        default:
            std::wcout << L"无效选择，请重新输入." << std::endl;
            break;
        }
    }
    
    return 0;
}
