@echo off
echo ========================================
echo Shellcode加载器快速测试脚本
echo ========================================

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 此脚本需要管理员权限
    echo 请右键点击并选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 管理员权限检查通过

REM 检查必要文件是否存在
if not exist "bin\hijack_tester.dll" (
    echo 错误: 未找到 bin\hijack_tester.dll
    echo 请先运行 build.bat 编译项目
    pause
    exit /b 1
)

if not exist "bin\test_target.exe" (
    echo 错误: 未找到 bin\test_target.exe
    echo 请先运行 build.bat 编译项目
    pause
    exit /b 1
)

if not exist "bin\test_injector.exe" (
    echo 错误: 未找到 bin\test_injector.exe
    echo 请先运行 build.bat 编译项目
    pause
    exit /b 1
)

echo 所有必要文件检查通过

echo.
echo ========================================
echo 启动测试目标程序
echo ========================================

REM 启动测试目标程序
echo 正在启动测试目标程序...
start "测试目标程序" bin\test_target.exe

REM 等待程序启动
echo 等待目标程序启动...
timeout /t 3 /nobreak >nul

REM 获取测试目标程序的PID
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq test_target.exe" /fo table /nh') do (
    set TARGET_PID=%%i
    goto :found_pid
)

echo 错误: 未找到测试目标程序进程
pause
exit /b 1

:found_pid
echo 找到测试目标程序，PID: %TARGET_PID%

echo.
echo ========================================
echo 设置环境变量并启动注入工具
echo ========================================

REM 设置环境变量
set TARGET_PID=%TARGET_PID%
echo 已设置 TARGET_PID=%TARGET_PID%

echo.
echo 现在将启动注入工具...
echo 请在注入工具中选择选项2，然后输入PID: %TARGET_PID%
echo.
pause

REM 启动注入工具
bin\test_injector.exe

echo.
echo ========================================
echo 测试完成
echo ========================================

echo 测试步骤已完成。
echo.
echo 如果测试成功，您应该看到:
echo 1. 测试目标程序正常运行
echo 2. 注入工具显示"注入完成"
echo 3. 计算器程序启动 (示例shellcode的效果)
echo.
echo 如果遇到问题，请检查:
echo 1. 是否以管理员身份运行
echo 2. 杀毒软件是否阻止了操作
echo 3. 查看日志文件: %TEMP%\shellcode_loader.log
echo.

pause
