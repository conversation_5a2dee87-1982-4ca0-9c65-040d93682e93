# Shellcode加载器 - 进程迁移技术实现

这是一个基于进程迁移技术的shellcode加载器，能够向指定PID的进程注入并执行shellcode。

## 功能特性

- **进程挖空**: 使用NtUnmapViewOfSection API清空目标进程的主模块
- **Shellcode注入**: 在目标进程中分配内存并写入shellcode
- **进程迁移**: 通过创建远程线程执行注入的shellcode
- **线程管理**: 自动暂停和恢复目标进程的所有线程
- **错误处理**: 完善的错误处理和调试输出机制

## 文件结构

```
hijack_tester/
├── dllmain.cpp          # 主要的shellcode加载器实现
├── test_injector.cpp    # 测试工具程序
├── test_target.cpp      # 测试目标程序
├── framework.h          # Windows框架头文件
├── pch.h               # 预编译头文件
├── pch.cpp             # 预编译源文件
├── build.bat           # 自动构建脚本
├── quick_test.bat      # 快速测试脚本
└── README.md           # 说明文档
```

## 编译说明

### 自动构建（推荐）
1. 以管理员身份运行命令提示符
2. 执行构建脚本：
   ```cmd
   build.bat
   ```
3. 脚本将自动编译所有组件到`bin`目录

### 手动编译
#### 编译DLL
```cmd
cl /LD /O2 /MD dllmain.cpp pch.cpp /Fe:hijack_tester.dll
```

#### 编译测试工具
```cmd
cl test_injector.cpp /Fe:test_injector.exe
cl test_target.cpp /Fe:test_target.exe /link psapi.lib
```

## 使用方法

### 快速测试（推荐）
1. 以管理员身份运行：
   ```cmd
   quick_test.bat
   ```
2. 脚本将自动启动测试目标程序并引导您完成注入测试

### 方法1: 手动交互测试
1. 以管理员身份运行`bin\test_target.exe`（测试目标程序）
2. 记录显示的进程ID
3. 以管理员身份运行`bin\test_injector.exe`
4. 选择操作选项：
   - 列出运行进程
   - 注入DLL到指定PID
   - 注入DLL到指定进程名

### 方法2: 环境变量方式
1. 设置环境变量指定目标PID：
   ```cmd
   set TARGET_PID=1234
   ```
2. 使用任何DLL注入工具将`bin\hijack_tester.dll`注入到目标进程

### 方法3: 程序化调用
```cpp
// 创建shellcode加载器
ShellcodeLoader loader;

// 设置目标进程PID
if (loader.SetTargetPID(targetPID)) {
    // 执行进程挖空
    if (loader.HollowProcess()) {
        // 注入shellcode
        if (loader.InjectShellcode(shellcode, size)) {
            // 执行shellcode
            loader.ExecuteShellcode();
        }
    }
}
```

## 技术实现

### 进程挖空流程
1. 打开目标进程句柄
2. 暂停目标进程的所有线程
3. 获取主模块信息
4. 使用NtUnmapViewOfSection取消映射主模块
5. 为shellcode分配新的内存空间

### Shellcode注入流程
1. 在目标进程中分配可执行内存
2. 将shellcode写入分配的内存
3. 创建远程线程执行shellcode
4. 恢复目标进程的线程执行

### 安全特性
- 自动线程管理，避免进程崩溃
- 完整的资源清理机制
- 详细的错误处理和日志记录
- 支持调试模式输出

## 示例Shellcode

当前包含一个示例shellcode（启动计算器），您可以替换为自己的shellcode：

```cpp
unsigned char your_shellcode[] = {
    // 在这里放置您的shellcode字节
    0xfc, 0x48, 0x83, 0xe4, 0xf0, ...
};
```

## 注意事项

1. **权限要求**: 需要管理员权限才能注入到其他进程
2. **目标进程**: 确保目标进程正在运行且可访问
3. **架构匹配**: DLL和目标进程必须是相同架构（x86/x64）
4. **杀毒软件**: 可能被杀毒软件检测，仅用于合法的安全测试
5. **调试模式**: 在Debug模式下会输出详细的调试信息

## 调试信息

在Debug模式下，程序会输出详细的调试信息到OutputDebugString，可以使用DebugView等工具查看：

- 进程打开状态
- 模块信息
- 线程操作结果
- 内存分配状态
- 注入执行结果

## 免责声明

此工具仅用于安全研究和合法的渗透测试目的。使用者需要确保：
- 仅在授权的环境中使用
- 遵守当地法律法规
- 不用于恶意目的
- 承担使用风险

## 技术支持

如有技术问题，请检查：
1. 目标进程是否正在运行
2. 是否具有足够的权限
3. DLL和目标进程架构是否匹配
4. 查看调试输出获取详细错误信息
