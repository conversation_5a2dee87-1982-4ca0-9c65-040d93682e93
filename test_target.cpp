#include <Windows.h>
#include <iostream>

// 简单的测试目标程序
// 这个程序将作为shellcode注入的目标进程

int main() {
    std::cout << "=== Shellcode注入测试目标程序 ===" << std::endl;
    std::cout << "进程ID: " << GetCurrentProcessId() << std::endl;
    std::cout << "程序正在运行，等待注入..." << std::endl;
    std::cout << "按任意键退出程序" << std::endl;
    
    // 显示进程信息
    HANDLE hProcess = GetCurrentProcess();
    PROCESS_MEMORY_COUNTERS pmc;
    if (GetProcessMemoryInfo(hProcess, &pmc, sizeof(pmc))) {
        std::cout << "当前内存使用: " << pmc.WorkingSetSize / 1024 << " KB" << std::endl;
    }
    
    // 获取主模块信息
    HMODULE hModule = GetModuleHandle(NULL);
    if (hModule) {
        char modulePath[MAX_PATH];
        if (GetModuleFileNameA(hModule, modulePath, MAX_PATH)) {
            std::cout << "主模块路径: " << modulePath << std::endl;
        }
        std::cout << "主模块基址: 0x" << std::hex << (uintptr_t)hModule << std::dec << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "使用方法:" << std::endl;
    std::cout << "1. 记录上面显示的进程ID" << std::endl;
    std::cout << "2. 运行 test_injector.exe" << std::endl;
    std::cout << "3. 选择注入到此进程ID" << std::endl;
    std::cout << "4. 观察是否成功执行shellcode" << std::endl;
    std::cout << std::endl;
    
    // 保持程序运行
    int counter = 0;
    while (true) {
        Sleep(1000);
        counter++;
        
        // 每10秒显示一次状态
        if (counter % 10 == 0) {
            std::cout << "程序运行中... (" << counter << "秒)" << std::endl;
            
            // 检查是否有键盘输入
            if (_kbhit()) {
                char ch = _getch();
                std::cout << "检测到按键，程序退出" << std::endl;
                break;
            }
        }
        
        // 检查是否有键盘输入
        if (_kbhit()) {
            char ch = _getch();
            std::cout << "检测到按键，程序退出" << std::endl;
            break;
        }
    }
    
    return 0;
}
