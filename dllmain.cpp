#include <Windows.h>

#define CCH_RM_MAX_APP_NAME 255
#define CCH_RM_MAX_SVC_NAME 255

typedef struct _RM_UNIQUE_PROCESS
{
    DWORD dwProcessId;
    FILETIME ProcessStartTime;
} RM_UNIQUE_PROCESS;

typedef struct _RM_PROCESS_INFO
{
    RM_UNIQUE_PROCESS Process;
    WCHAR strAppName[CCH_RM_MAX_APP_NAME + 1];
    WCHAR strServiceShortName[CCH_RM_MAX_SVC_NAME + 1];
    DWORD dwApplicationType;
    ULONG AppStatus;
    DWORD dwErrorStatus;
    DWORD dwRestartableStatus;
} RM_PROCESS_INFO;

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            
        }
        break;

    case DLL_THREAD_ATTACH:
        break;

    case DLL_THREAD_DETACH:
        break;

    case DLL_PROCESS_DETACH:
        
        break;
    }
    return TRUE;
}

