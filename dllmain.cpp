#include <Windows.h>

#define CCH_RM_MAX_APP_NAME 255
#define CCH_RM_MAX_SVC_NAME 255

int SleepAddr = 0;

typedef struct _RM_UNIQUE_PROCESS
{
    DWORD dwProcessId;
    FILETIME ProcessStartTime;
} RM_UNIQUE_PROCESS;

typedef struct _RM_PROCESS_INFO
{
    RM_UNIQUE_PROCESS Process;
    WCHAR strAppName[CCH_RM_MAX_APP_NAME + 1];
    WCHAR strServiceShortName[CCH_RM_MAX_SVC_NAME + 1];
    DWORD dwApplicationType;
    ULONG AppStatus;
    DWORD dwErrorStatus;
    DWORD dwRestartableStatus;
} RM_PROCESS_INFO;

extern "C" __declspec(dllexport) DWORD RmRegisterResources(DWORD dwSessionHandle, UINT nFiles, LPCWSTR rgsFilenames[], UINT nApplications, RM_UNIQUE_PROCESS rgApplications[], UINT nServices, LPCWSTR rgsServiceNames[])
{
    return 0;
}

extern "C" __declspec(dllexport) DWORD RmStartSession(DWORD* pSessionHandle, DWORD dwSessionFlags, WCHAR strSessionKey[])
{
    return 0;
}

extern "C" __declspec(dllexport) DWORD RmGetList(DWORD dwSessionHandle, UINT* pnProcInfoNeeded, UINT* pnProcInfo, RM_PROCESS_INFO rgAffectedApps[], LPDWORD lpdwRebootReasons)
{
    return 0;
}

extern "C" __declspec(dllexport) DWORD RmEndSession(DWORD dwSessionHandle)
{
    return 0;
}

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        MessageBox(NULL, L"Process Attached", L"Notification", MB_OK);
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

