@echo off
echo ========================================
echo Shellcode加载器构建脚本
echo ========================================

REM 检查Visual Studio环境
if not defined VCINSTALLDIR (
    echo 正在设置Visual Studio环境...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if errorlevel 1 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if errorlevel 1 (
            echo 错误: 未找到Visual Studio环境
            echo 请确保已安装Visual Studio 2019或2022
            pause
            exit /b 1
        )
    )
)

echo Visual Studio环境已设置

REM 创建输出目录
if not exist "bin" mkdir bin
if not exist "obj" mkdir obj

echo.
echo ========================================
echo 编译DLL (Release x64)
echo ========================================

REM 编译DLL
cl.exe /c ^
    /O2 ^
    /MD ^
    /D "WIN32" ^
    /D "NDEBUG" ^
    /D "_WINDOWS" ^
    /D "_USRDLL" ^
    /D "HIJACK_TESTER_EXPORTS" ^
    /I "." ^
    /Fo"obj\\" ^
    dllmain.cpp pch.cpp

if errorlevel 1 (
    echo 编译失败!
    pause
    exit /b 1
)

REM 链接DLL
link.exe ^
    /DLL ^
    /OUT:"bin\hijack_tester.dll" ^
    /SUBSYSTEM:WINDOWS ^
    /MACHINE:X64 ^
    /NOLOGO ^
    kernel32.lib user32.lib ^
    obj\dllmain.obj obj\pch.obj

if errorlevel 1 (
    echo 链接失败!
    pause
    exit /b 1
)

echo DLL编译成功: bin\hijack_tester.dll

echo.
echo ========================================
echo 编译测试工具
echo ========================================

REM 编译测试工具
cl.exe ^
    /O2 ^
    /MD ^
    /D "WIN32" ^
    /D "NDEBUG" ^
    /D "_CONSOLE" ^
    /Fe:"bin\test_injector.exe" ^
    test_injector.cpp ^
    kernel32.lib user32.lib

if errorlevel 1 (
    echo 测试工具编译失败!
    pause
    exit /b 1
)

echo 测试工具编译成功: bin\test_injector.exe

echo.
echo ========================================
echo 编译测试目标程序
echo ========================================

REM 编译测试目标程序
cl.exe ^
    /O2 ^
    /MD ^
    /D "WIN32" ^
    /D "NDEBUG" ^
    /D "_CONSOLE" ^
    /Fe:"bin\test_target.exe" ^
    test_target.cpp ^
    kernel32.lib user32.lib psapi.lib

if errorlevel 1 (
    echo 测试目标程序编译失败!
    pause
    exit /b 1
)

echo 测试目标程序编译成功: bin\test_target.exe

echo.
echo ========================================
echo 编译Debug版本 (可选)
echo ========================================

set /p choice="是否编译Debug版本? (y/n): "
if /i "%choice%"=="y" (
    echo 编译Debug版本...
    
    REM 编译Debug DLL
    cl.exe /c ^
        /Od ^
        /MDd ^
        /D "WIN32" ^
        /D "_DEBUG" ^
        /D "_WINDOWS" ^
        /D "_USRDLL" ^
        /D "HIJACK_TESTER_EXPORTS" ^
        /I "." ^
        /Fo"obj\debug_" ^
        dllmain.cpp pch.cpp
    
    if errorlevel 1 (
        echo Debug编译失败!
        pause
        exit /b 1
    )
    
    REM 链接Debug DLL
    link.exe ^
        /DLL ^
        /DEBUG ^
        /OUT:"bin\hijack_tester_debug.dll" ^
        /SUBSYSTEM:WINDOWS ^
        /MACHINE:X64 ^
        /NOLOGO ^
        kernel32.lib user32.lib ^
        obj\debug_dllmain.obj obj\debug_pch.obj
    
    if errorlevel 1 (
        echo Debug链接失败!
        pause
        exit /b 1
    )
    
    echo Debug版本编译成功: bin\hijack_tester_debug.dll
)

echo.
echo ========================================
echo 构建完成!
echo ========================================
echo.
echo 输出文件:
echo   - bin\hijack_tester.dll (Release版本)
echo   - bin\test_injector.exe (测试工具)
echo   - bin\test_target.exe (测试目标程序)
if exist "bin\hijack_tester_debug.dll" echo   - bin\hijack_tester_debug.dll (Debug版本)
echo.
echo 使用方法:
echo   方法1 - 交互式测试:
echo     1. 以管理员身份运行 bin\test_target.exe
echo     2. 记录显示的进程ID
echo     3. 以管理员身份运行 bin\test_injector.exe
echo     4. 选择注入到目标进程ID
echo.
echo   方法2 - 环境变量方式:
echo     1. 设置 TARGET_PID 环境变量
echo     2. 使用任何DLL注入工具注入 hijack_tester.dll
echo.
echo 注意: 需要管理员权限才能注入到其他进程
echo.

pause
